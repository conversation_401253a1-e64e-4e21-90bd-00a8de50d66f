<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
    />
    <meta content="always" name="referrer" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <!--忽略页面中的数字识别为电话，忽略email识别,去除Android平台中对邮箱地址的识别-->
    <meta content="telephone=no,email=no" name="format-detection" />
    <!--windows phone 点击无高光-->
    <meta name="msapplication-tap-highlight" content="no" />
    <!--启用360浏览器的极速模式(webkit)-->
    <meta name="renderer" content="webkit|ie-comp|ie-stand" />
    <!-- UC应用模式 -->
    <meta name="browsermode" content="application" />
    <!--UC强制竖屏-->
    <meta name="screen-orientation" content="portrait" />
    <!--QQ强制竖屏-->
    <meta name="x5-orientation" content="portrait" />
    <!--windows phone 点击无高光-->
    <meta name="msapplication-tap-highlight" content="no" />

    <title><%= webpackConfig.name %></title>
    <script src="https://www.cmpassport.com/h5/js/jssdk_yw_auth/jssdk.min.js"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script type="text/javascript" src="https://res.app.coc.10086.cn/appother/js/public/cmcc-v1.0.1.js"></script>
    <!-- <script type="text/javascript" src="https://res.app.coc.10086.cn/appother/js/public/cmcc.js"></script> -->
    <!-- <script src="https://sc.bj.chinamobile.com/js/cmcc/bj_sdc_load_10.12.8.9_3s3i_5v8r.js"></script> -->
    <script type="text/javascript">
      var isDebuuger = window.location.href.indexOf('isDebugger=1') > -1
      var isLocal = ''
      var initParams = {
          "autotrack": false,
          "compress": !isDebuuger,
          "hashtag": true,
          "multiTrack": [
            {
              "sdkType": "WT",
              "host":'wtrace.bj.chinamobile.com',
              "dcsid":'dcs0sp3hxdjgi9wo99cdykznx_5v8r',
              "branch": "bjH5"
            },
            {
              "sdkType": "GIO",
              "host": "jiguang.coc.10086.cn",
              "scheme": "https",
              "projectId": '9e4e5fa7244c6b6e',
              "dataSourceId": 'a7464be8b200fe24',
              "sdkPath": "https://h5.bj.10086.cn/js/cmcc/gdp.js"
            }
          ]
        };

        !(function(e, n, t, c, p) {
          (t = n.createElement('script'));
          s = n.getElementsByTagName('script')[0];
          (t.async = 1), (t.src = c),
  s.parentNode.insertBefore(t, s);
          t.onload = function(){
                  // 初始化js
            <% if (process.env.NODE_ENV === 'development'){ %>
            wtio('ignoreLocalRule',true);
            <% } %>
            wtio('init', p);
            wtio('track', 'pageview' , {
            'WT.et': 'pageview',
            'WT.event': 'H5PageShow',
            'WT.es': window.location.href
          });
          }
        })(
          window,
          document,
          'script',
          'https://h5.bj.10086.cn/js/cmcc/dcs_wtio.js', //此部分替换为您的 js 文件路径
          initParams
        );
    </script>
  </head>

  <body>
    <div id="app"></div>
    <script>
      // // console.log('版本1')
      function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
      }
      if (window.location.port == '7443' || window.location.port == '7080' || getQueryString('isDebugger')) {
        var sdc_url = 'https://sc.bj.chinamobile.com/js/vConsole.js'
        var s = document.createElement("script"); s.async = true; s.src = sdc_url;
        var s2 = document.getElementsByTagName("script")[0];
        s2.parentNode.insertBefore(s, s2);
        s.onload = function() {
          new VConsole()
        }
      }
      // 用于出发css伪类active效果
      if ("ontouchstart" in document.documentElement) {
        document.body.addEventListener("touchstart", function () {});
      }
    </script>
  </body>
</html>
