/*
 * @Author: zhen<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-24 19:08:31
 * @LastEditors: zhengwenling <EMAIL>
 * @LastEditTime: 2025-03-03 15:12:06
 * @FilePath: \multiple-project\src\projects\checkin-new\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* eslint-disable no-new */
import '@/utils/amfe-flexible'
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
// By default we import all the components.
// Only reserve the components on demand and remove the rest.
// Style is always required.
// import '@/utils/config/link'
import './styles/index.scss'
import '@/utils/vantUI'
import App from './App'
import router from './router'

import Loading from '@/components/Loading'
import AspDialog from './components/AspDialog'
import RuleDialog from './components/RuleDialog'
import AspLoading from './components/AspLoading'
// 项目公共模块
import actMixins from '@/mixins'
// 活动公共模块
import mixins from './mixins'
import VueLuckyCanvas from '@lucky-canvas/vue'
import * as filters from './filters'
import store from './store'
import './utils/const-id'
import './utils/activity'
// require('./mock/index')
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
Vue.use(Loading)
Vue.use(RuleDialog)
Vue.use(AspDialog)
Vue.use(AspLoading)
Vue.mixin(mixins)
Vue.mixin(actMixins)
Vue.use(VueLuckyCanvas)

Vue.config.productionTip = false
Vue.config.ignoredElements = ['wx-open-launch-app', 'wx-open-launch-weapp']

if (process.env.NODE_ENV === 'development') {
  sessionStorage['set' + 'Item']('userToken', 'yus')
  require('./mock/index')
  initVue()
} else {
  initVue()
}
function initVue () {
  new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
  })
}

// window.onload = () => {
//   jtWebtrends1.setGeneralProps()
//   jtWebtrends1.gdpCommonTrack('pageview', 'H5PageShow')
// }
