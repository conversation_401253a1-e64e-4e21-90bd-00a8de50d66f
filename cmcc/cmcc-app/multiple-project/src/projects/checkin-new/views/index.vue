<template>
  <div class="index">
    <notice-dialog v-if="region && region.length > 0 && counts < 3" :block="region[0]" :ckwm="ckwm" :gbwm="gbwm"></notice-dialog>
    <notice-bar v-if="region && region.length > 1" :text="region[1].title"></notice-bar>
    <!-- 顶部背景图 -->
    <div class="index__banner"></div>
    <!-- <img src="../assets/index/bg-area.png" alt="" class="index__banner" /> -->
    <!-- 顶部通知栏 -->
    <!-- <div class="index__top">
      <van-notice-bar left-icon="volume-o" text="原签到活动暂迁入“领心愿金”入口，点击即可进入哦~" :scrollable="false" />
    </div> -->
    <!-- 顶部活动规则+中奖记录 -->
    <div class="index-tips index-icons">
      <p class="index__rule" @click="toRule"></p>
      <p class="index__prize" @click="toPrizeList"></p>
    </div>
    <!-- 顶部中奖记录右上角提示 -->
    <p class="index__guide" v-if="queryPrize">签到奖励请在这里查看哟</p>
    <!-- 顶部轮播 -->
    <topBanner />
    <!-- 签到主体 -->
    <div class="index-main index-icons--before">
      <checkinTop :haveToken="haveToken" />
      <checkin
        :signInList="signInList"
        :isSignIn="isSignIn"
        :currentDate="currentDate"
        :signFullStatus.sync="signFullStatus"
        :FullCheckinPopupImgurl="FullCheckinPopupImgurl"
        @checkin="checkin"
      />
      <actsignin2023Icon :region="actsignIconRegion" />
      <sky-draw :isSignIn="isSignIn" :tjlb.sync="tjlb" :tjlbPrize="tjlbPrize" :misdnmask="misdnmask" @queryTjlbInfo="queryTjlbInfo" @showCheckinPrize="showCheckinPrize" />
      <div v-if="showFamilyTask && fmlUpgradeMsg.imgurl" class="family-task" :style="{backgroundImage: `url(${fmlUpgradeMsg.imgurl})`}" @click="goFamilyTask"></div>
      <notice-waistband v-else class="waistbands" :waistbandBanner="waistbandBanner" :wm="wm"></notice-waistband>
    </div>
    <!-- 年底反馈 -->
    <feedback v-if="haveToken" :isSignIn="isSignIn" @checkin="checkin" @showLXYQRule="showLXYQRule" />
    <!-- 翻牌子 -->
    <div class="wrap">
      <turn-card
        :isSignIn="isSignIn"
        :browseTask="browseTask"
        :visitStatus="visitStatus"
        :usedCount="usedCount"
        :count="count"
        :currentDate.sync="currentDate"
        @doPrizeFpz="doPrizeFpz"
        @doTaskSuccess="doTaskSuccess"
        @showUpgrade="showUpgrade"
        @changeShowGradePopup="changeShowGradePopup"
      />
      <!-- 底部业务-长短期活动入口 -->
      <region :regionList="regionList" />
    </div>
    <!-- 底部业务-精彩推荐 -->
    <business-floor :business-floor-data-sign="businessFloorDataSign" :business-floor-data-operate="businessFloorDataOperate" />
    <!-- 底部业务-banner -->
    <ActSignIn2022_banner :bannerList="bannerList"/>
    <!-- 分享成功获取次数弹窗 -->
    <popup-prize :showPopup.sync="shareStatus" popupType="sharePopup" :shareNum="shareNum" @closePopup="shareStatus = false" ></popup-prize>
    <!-- 悬浮窗 -->
    <notice-float :floatData="floatData" :floatClickWm="floatClickWm"></notice-float>
    <!-- 升级弹窗 -->
    <upgrade-popup ref="upgrade"></upgrade-popup>
    <!-- 底部业务-iframe -->
    <iframe class="is-iframe" v-for="(item, index) in iframeList" :key="index" :src="item.url"></iframe>
    <!-- 满意度弹窗 -->
    <collection-suspend v-if="haveToken" :showGradePopup.sync="showGradePopup"></collection-suspend>
    <!-- 满签弹窗（运营位配置，1月31日下线） -->
    <full-checkin-popup :showFullCheckinPopup.sync="showFullCheckinPopup" :FullCheckinPopupImgurl="FullCheckinPopupImgurl" />
    <!-- 家庭圈更新弹窗 -->
    <family-update :showFamilyUpdate.sync="showFamilyUpdate" />
  </div>
</template>

<script>
import { queryFamilyCircleZi } from '../api/index'
import { Overlay } from 'vant'
import { mapActions, mapState } from 'vuex'
import { encoperate_unifyH5 } from '@/api/common.js'
import { getQueryString, isIosReload } from '@/utils/utils'
import jtWebtrends1 from '@/utils/jtWebtrends'
import noticeDialog from '@/components/notice-dialog/index.vue'
import noticeBar from '@/components/notice-bar/index.vue'
import noticeFloat from '@/components/notice-float/index.vue'
import noticeWaistband from '@/components/notice-waistband/index.vue'
import fullCheckinPopup from './components/full-checkin-popup.vue'
import familyUpdate from './components/family-update.vue'
import { getcheckinResult } from '../utils/checkin'
import { cmccLeadeonSSo } from '../../../../../../../bjapp-model/vue2/js/login/jt-login'
import { newWebview } from '../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import CHANNEL from '../../../../../../../bjapp-model/vue2/js/channel'
import { getSignIn, doPrize, getSponsor, operate_unifyH5, queryStatus, assistantSponsor, deleteNotice, queryAwardInfo, queryTjlbInfo } from '../api/index'
import { converMBToGB, getMisdnmask, dateFormat, checkBjUser, listGetHavePrize } from '../utils/utils'
import { SUCCESS_CODE, DEFAULT_AVATARURL, IS_YW_JMESS } from '../config/index'
import { actRule, actRule1 } from '../config/rule'
import topBanner from '../components/yw/top_banner.vue'
import businessFloor from '../components/yw/actsignin_jcbannerjt.vue'
import actsignin2023Icon from "../components/yw/actsignin2023_icon.vue"
import ActSignIn2022_banner from '../components/yw/ActSignIn2022_banner.vue'
import region from '../components/yw/styles_index.vue'
import checkin from './components/checkin/index.vue'
import TurnCard from './components/turn-card/index.vue'
import feedback from './components/feedback/index.vue'
import popupPrize from './components/turn-card/components/popup-prize.vue'
import upgradePopup from './components/upgrade-popup.vue'
import collectionSuspend from './components/collection-suspend/index'
import skyDraw from './components/sky-draw/index.vue'
import checkinTop from './components/checkin/index-top.vue'
import JTFK from '../utils/JTFK'
import { links } from '@/projects/checkin-new/config/link'
export default {
  components: {
    checkin,
    TurnCard,
    region,
    feedback,
    noticeDialog,
    noticeBar,
    noticeFloat,
    noticeWaistband,
    businessFloor,
    popupPrize,
    upgradePopup,
    [Overlay.name]: Overlay,
    skyDraw,
    collectionSuspend,
    actsignin2023Icon,
    ActSignIn2022_banner,
    topBanner,
    checkinTop,
    fullCheckinPopup,
    familyUpdate
  },
  data() {
    return {
      showFamilyUpdate: false, // 家庭圈更新弹窗
      userType: -1,
      region: [], // 弹框和置顶轮动条信息
      counts: 0, // 根据次数显示弹框，小于2显示
      waistbandBanner: null, // 腰封
      actsignIconRegion: {},
      actsignTasks: {},
      floatData: {}, // 悬浮窗
      ckwm: {// 查看详情插码
        event: 'P00000040863',
        eventName: '中国移动APP每日签到_活动弹窗_查看详情'
      },
      gbwm: {// 关闭弹窗插码
        event: 'P00000040864',
        eventName: '中国移动APP每日签到_活动弹窗_关闭'
      },
      wm: {// 腰封插码
        event: 'P00000090565',
        eventName: '中国移动APP每日签到_腰封'
      },
      floatClickWm: {// 点击悬浮窗插码
        event: 'P00000045340',
        eventName: '中国移动APP每日签到_悬浮窗'
      },
      userToken: sessionStorage.getItem('userToken'),
      haveToken: false,
      DEFAULT_AVATARURL: DEFAULT_AVATARURL,
      iframeList: [],
      openIds: '', // 好友openIds
      helpSponsor: '', // 分享标志
      isNotBjUser: false,
      visitStatus: false, // 当天浏览任务状态
      browseTask: {}, // 浏览任务
      usedCount: 0, // 今日已用翻牌次数
      count: 0, // 今日可用翻牌次数
      shareStatus: false, // 分享通知状态
      shareNum: 0, // 分享次数
      visitShare: false, // 浏览通知状态
      regionList: [], // 运营位配置
      headConfig: { imgurl: require('@/projects/checkin-new/assets/index/bg-area.png') }, // 头图配置
      backgroundUrl: require('@/projects/checkin-new/assets/index/bg.png'), // 背景图配置
      newUser: false, // 首次进入弹窗
      queryPrize: false, // 进入过我的奖品页
      bannerList: [], // 底部banner数据
      businessFloorDataSign: [], // 签到精彩推荐
      businessFloorDataOperate: [], // 运营位
      floatMoudleData: {}, // 悬浮运营位数据
      isuser: false, // 是否展示手厅签到简化版
      signFullStatus: 0, // 满签状态
      tjlb: {
        isPop: false,
        tjlbStatus: 0,
        isSendSubscribe: false
      },
      tjlbPrize: {
        tjlb1: JSON.parse(sessionStorage['get' + 'Item']('tjlb1') || '{}') || {},
        tjlb2: JSON.parse(sessionStorage['get' + 'Item']('tjlb2') || '{}') || {}
      },
      showGradePopup: false, // 是否展示奖品弹窗后的满意度弹窗
      showFullCheckinPopup: false, // 满签日历弹窗
      FullCheckinPopupImgurl: '', // 满签日历弹窗图片地址
      timestamp: 0, // 服务器时间戳
      showLxyq: false
    }
  },
  created() {
    // ios物理返回强制刷新
    if (CHANNEL.isIOS()) isIosReload()
    this.$loading.show()
  },
  activated() {
    // 测试时间
    this.updateTestTime(false)
    if (this.userToken) {
        this.loginInit()
    } else {
      cmccLeadeonSSo(() => {
        this.loginInit()
      })
    }
  },
  computed: {
    ...mapState({
      signInList: state => state.signInList,
      currentDate: state => state.currentDate,
      misdnmask: state => state.misdnmask,
      isSignIn: state => state.isSignIn,
      signCardNum: state => state.signCardNum,
      testTime: state => state.testTime,
      fingerprintUrl: state => state.fingerprintUrl,
      fingerprintId: state => state.fingerprintId,
      isMigrate: state => state.isMigrate,
      fmlUpgradeMsg: state => state.fmlUpgradeMsg
    }),
    showFamilyTask() {
      return this.isMigrate === 0 && (this.userType === 1 || this.userType === 2 || this.userType === 3)
    }
  },
  mounted() {},
  methods: {
    ...mapActions(['updateTestTime', 'updateSignInList', 'updateCurrentdate', 'updateMisdnmask', 'updateIsSign', 'updateSignCardNum', 'updateActsigntaskregion', 'updateFingerprintUrl', 'updateFingerprintId', 'updateIsMigrate', 'updateFmlUpgradeMsg']),
    converMBToGB,
    getMisdnmask,
    // 登录后初始化
    loginInit() {
      this.haveToken = true
      this.initPrizeInfo()
      this.getEncoperate_unifyH5()
      this.queryTjlbInfo()
      this.queryFamilyTask()
    },
    // 查询家庭圈子状况
    queryFamilyTask() {
      queryFamilyCircleZi({}).then(res => {
        this.updateIsMigrate(res.isMigrate)
        this.userType = res.userType
      }).catch(() => {})
    },
    // 跳转家庭圈任务
    goFamilyTask() {
      if (this.userType === 3) {
        this.showFamilyUpdate = true
      } else {
        newWebview(CHANNEL.isGray() ? links.jtzq.gray : links.jtzq.master)
      }
    },
    // 获取腰封置顶轮动和弹框信息
    getEncoperate_unifyH5() {
      const params = {
        cateKeyword: 'singin_jt_yaofeng,allactivityjt_waistband,actsignin2022jt_top,signin_jt_popup,actsignin2023_icon,actsignin2023_task,actsignin2023_prizeBanner,actsignin2023_pop,fmlUpgrade',
        actKeyword: 'actsignin2022JT'
      }
      encoperate_unifyH5(params).then(res => {
        if (res.result === 0) {
          res.region.forEach(item => {
            if (item.keyword === 'singin_jt_yaofeng') { // 优先腰封
              this.waistbandBanner = item.block
            } else if (item.keyword === 'allactivityjt_waistband' && this.waistbandBanner === null) {
              // 腰封
              this.waistbandBanner = item.block
            } else if (item.keyword === 'actsignin2022jt_top') {
              // 弹窗和置顶滚顶
              this.region = item.block
            } else if (item.keyword === 'signin_jt_popup') {
              this.floatData = item.block[0]
            } else if(item.keyword === 'actsignin2023_icon') {
              this.actsignIconRegion = item
            } else if(item.keyword === 'actsignin2023_task') {
              this.updateActsigntaskregion(item)
            } else if(item.keyword === 'actsignin2023_prizeBanner' && item.block && item.block.length > 0) {
              sessionStorage.setItem('BANNER_LIST', JSON.stringify(item.block))
            } else if(item.keyword === 'actsignin2023_pop') {
              this.FullCheckinPopupImgurl = item.block[0].imgurl || ''
              if(res.weekCount <= 1) {
                this.showFullCheckinPopup = true
              }
            } else if (item.keyword === 'fmlUpgrade') {
              item.block.forEach(iItem => {
                if (iItem.title === 'checkin') {
                  this.updateFmlUpgradeMsg(iItem || {})
                }
              })
            }
          })
          this.counts = res.count
        }
      })
    },
    // 查询天降礼包状态
    queryTjlbInfo() {
      queryTjlbInfo({}).then(res => {
        const { result, isPop, tjlbStatus, isSendSubscribe, signFullStatus } = res
        this.signFullStatus = signFullStatus
        if (result === 0) {
          this.tjlb = {
            isPop: isPop && !sessionStorage.getItem('isTjlbPop'),
            tjlbStatus,
            isSendSubscribe
          }
        }
      })
    },
    // 查询配置的奖品信息
    initPrizeInfo() {
      const prizeParams = sessionStorage.getItem('PRIZE_PARAMS')
      if (prizeParams) {
        this.init()
        return
      }
      queryAwardInfo({}).then(res => {
        const { result, list, timestamp } = res
        const PRIZE_PARAMS = {}
        const PRIZE_DAYS = []
        const PRIZE_DATES = []
        const nowDate = new Date(timestamp).getDate()
        this.timestamp = timestamp
        if (result === 0 && list && list.length > 0) {
          list.forEach(item => {
            const name = item.awardName.replace('prize', '') // 为数字的话，则是配置的签到次数的奖品信息
            const date = item.awardName.replace('date', '') // 指定日期配置的奖品
            // if (item.prize.length > 0) { // 展示奖品名称的时候不出现这个
            //   item.prize[0].prizeName = item.prize[0].prizeName.replace(/(\(|（)次月生效(\)|）)/, '')
            // }
            if (!isNaN(parseInt(name)) && item.prize.length > 0) { // 设置签到次数奖品信息
              const prize = item.prize[0]
              PRIZE_DAYS.push(name)
              PRIZE_PARAMS[name] = {
                ...prize,
                prizeImg: prize.imgurl
              }
            } else if (item.awardName === 'signFull') { // 满签的奖品设置
              sessionStorage['set' + 'Item']('signFullPrize', JSON.stringify(item.prize))
            } else if (item.awardName === 'tjlb1') { // 天降礼包1
              this.tjlbPrize.tjlb1 = item.prize && item.prize[0]
              sessionStorage['set' + 'Item']('tjlb1', JSON.stringify(this.tjlbPrize.tjlb1))
            } else if (item.awardName === 'tjlb2') { // 天降礼包2
              this.tjlbPrize.tjlb2 = item.prize && item.prize[0]
              sessionStorage['set' + 'Item']('tjlb2', JSON.stringify(this.tjlbPrize.tjlb2))
            } else if (date.length === 8) {
              const d = date.substring(0, 4) + '/' + date.substring(4, 6) + '/' + date.substring(6, 8)
              const t = new Date(d + ' 23:59:59')
              PRIZE_DATES.push({
                month: t.getMonth() + 1,
                day: t.getDate(),
                timestamp: t.getTime(), // 指定日期的时间戳
                date: d.replace(/\//ig, '-'), // 指定日期的日期展示
                difDays: t.getDate() - nowDate, // 当前时间到指定日期相聚的天数
                prizeObj: { ...item.prize[0], prizeImg: item.prize[0].imgurl } // 配置的奖品
              })
            }
          })
          PRIZE_DAYS.sort((a, b) => parseInt(a) - parseInt(b)) // 设置排序
          PRIZE_DATES.sort((a, b) => parseInt(a.timestamp) - parseInt(b.timestamp))
          sessionStorage['set' + 'Item']('PRIZE_PARAMS', JSON.stringify(PRIZE_PARAMS))
          sessionStorage['set' + 'Item']('PRIZE_DAYS', JSON.stringify(PRIZE_DAYS))
          sessionStorage['set' + 'Item']('PRIZE_DATES', JSON.stringify(PRIZE_DATES))
        }
      }).finally(() => {
        this.init()
      })
    },
    //  查询运营位信息、助力信息、签到信息
    init() {
      const axiosList = [this.getSignIn(), this.getOperate(), this.drawStatusSt(), this.getSponsor()]
      if (getQueryString('fromInvite')) {
        this.assistantSponsor()
      }
      Promise.all(axiosList)
        .then((resultList) => {
          const { result } = resultList[0]
          this.getResult(result)
        })
        .catch((err) => {
          this.getResult(err.result)
        })
        .finally(() => {
          this.$loading.hide()
          jtWebtrends1.multiTrack('P00000025009', '中国移动APP每日签到_主页')
        })
    },
    // 查询用户签到信息接口
    getSignIn() {
      return new Promise((resolve, reject) => {
        getSignIn({ token: this.userToken })
          .then((res) => {
            const {
              result,
              timestamp,
              misdnmask,
              encMisdn,
              isBj,
              signInList,
              temptimestamp = 0,
              isSignIn,
              newUser,
              queryPrize,
              jtEncMisdn,
              signCardNum,
              fingerprintUrl = '',
              fingerprintId = ''
            } = res
            this.updateFingerprintUrl(fingerprintUrl)
            this.updateFingerprintId(fingerprintId)
            jtWebtrends1.setUserId(encMisdn, jtEncMisdn)
            sessionStorage['set' + 'Item']('isBjuser', isBj)
            if (isBj !== 1) {
              this.isNotBjUser = true
            }
            if (result === SUCCESS_CODE) {
              this.updateSignInList(signInList)
              this.updateIsSign(isSignIn)
              this.updateSignCardNum(signCardNum)
            }
            if (result === -20003) {
              sessionStorage['set' + 'Item']('isBjuser', 0)
            }
            // 返回是false就是弹过了 为true就是没弹
            this.newUser = newUser
            this.queryPrize = queryPrize
            this.updateCurrentdate(this.testTime ? new Date().getTime() : timestamp)
            this.updateMisdnmask(misdnmask)
            resolve(res)
          })
          .catch((err) => {
            // console.log(err)
            reject(err)
          })
          .finally(() => {
            if (!this.currentDate || this.testTime) {
              this.updateCurrentdate(new Date().getTime())
            }
          })
      })
    },
    // 获得分享标识
    getSponsor() {
      sessionStorage.removeItem('helpSponsor')
      return new Promise((resolve, reject) => {
        getSponsor({ token: this.userToken })
          .then((res) => {
            const { result, sponsor } = res
            if (result === SUCCESS_CODE) {
              this.helpSponsor = sponsor
              sessionStorage['set' + 'Item']('helpSponsor', sponsor)
            }
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 获取运营位
    getOperate() {
      const params = {
        // 栏目关键字，逗号分割 fanpaizi_renwubanner：翻牌子任务banner actsignin_bannerjt：长短期活动入口-banner actsignin_lunbojt：长短期活动入口-轮播 actqiandao_toutubanner:签到头图-banner ActSignIn2022_banner：底部banner轮播 jtqdxf: 悬浮运营位数据 actsignin2023_iframe：iframe数据
        cateKeyword: 'fanpaizijt_renwubanner,actsignin_bannerjt,actsignin_lunbojt,actqiandao_toutubanner,ActSignIn2022_banner,actsignin_jclunbojt,actsignin_jcbannerjt,jtqdxf,actsignin2023_iframe',
        trafficCategoryId: '',
        behaviorCode: '10831',
        explain: '签到运营位',
        os: 'ios',
        ver: 'bjservice_ios_8.3.2'
      }
      return new Promise((resolve, reject) => {
        operate_unifyH5(params)
          .then((res) => {
            const { region } = res
            this.regionList = []
            region.map((item) => {
              if (item.keyword === 'actsignin_bannerjt') {
                item.styleType = '01'
                this.regionList.push(item)
              }
              if (item.keyword === 'actsignin_lunbojt') {
                item.styleType = '02'
                this.regionList.push(item)
              }
              if (item.keyword === 'actqiandao_toutubanner' && item.block) {
                this.headConfig = item.block[0]
                this.backgroundUrl = item.backgroundurl
              }
              if (item.keyword === 'fanpaizijt_renwubanner') {
                this.browseTask = item
              }
              if (item.keyword === 'ActSignIn2022_banner' && item.block) {
                this.bannerList = item.block
              }
              if (item.keyword === 'actsignin_jclunbojt' && item.block) {
                this.businessFloorDataSign = item.block
              }
              if (item.keyword === 'actsignin_jcbannerjt' && item.block) {
                this.businessFloorDataOperate = item.block
              }
              if (item.keyword === 'jtqdxf' && item.block) {
                this.floatMoudleData = item.block[0]
              }
              if(item.keyword === 'actsignin2023_iframe' && item.block) {
                this.iframeList = item.block
              }
            })
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 查询翻牌状态接口
    drawStatusSt() {
      return new Promise((resolve, reject) => {
        queryStatus({ token: this.userToken })
          .then((res) => {
            const {
              result,
              isSignIn,
              visitStatus,
              count,
              shareStatus,
              shareNum,
              usedCount,
              visitShare
            } = res
            if (Number(result) === SUCCESS_CODE) {
              // 数据迁移
              if (!this.isSignIn) {
                this.updateIsSign(isSignIn)
              }
              this.visitStatus = visitStatus // 当天浏览任务状态
              this.usedCount = usedCount // 今日已用翻牌次数
              this.count = count // 今可用翻牌次数
              this.shareStatus = shareStatus // 分享助力通知状态
              this.shareNum = shareNum // 分享获得机会
              this.visitShare = visitShare // 浏览通知状态
              if (shareStatus) {// 分享成功弹框
                this.deleteNotice(1)
              }
              if (visitShare) {
                this.$toast('恭喜您完成任务，获得1次翻牌奖励')
                this.deleteNotice(2)
              }
            }
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 助力 TODO 改成小程序助力
    assistantSponsor(sponsor, wxToken) {
      assistantSponsor({ token: this.userToken, sponsor, wxToken })
        .then((res) => {
          const { result } = res
          if (Number(result) === SUCCESS_CODE) {
            this.$toast('助力成功')
          }
        })
        .catch(() => {})
    },
    // 是否展示连续月签规则
    showLXYQRule(value) {
      this.showLxyq = value
    },
    // 玩法说明
    toRule() {
      jtWebtrends1.multiTrack('P00000025010', '中国移动APP每日签到_活动规则')
      this.rulePopup(this.showLxyq ? actRule : actRule1)
    },
    // 领奖记录
    toPrizeList() {
      jtWebtrends1.multiTrack('P00000025012', '中国移动APP每日签到_我的奖品')
      if (!checkBjUser()) return
      this.$router.push({ name: 'Prize' })
    },
    // 签到
    async checkin() {
      if (!checkBjUser()) return
      if (!this.isSignIn) {
        this.$loading.show()
        let constid = ''
        if(this.fingerprintUrl && this.fingerprintId) {
          constid = await JTFK({ appId: this.fingerprintId, server: this.fingerprintUrl })
        }
        console.log('是否拿到id11', constid)
        doPrize({ token: this.userToken, type: 'sign', constid })
          .then((res) => {
            let { result, prizelist, timestamp } = res
            if (this.getResult(result)) {
              this.count += 1
              let prizeObj = {}
              prizelist = listGetHavePrize(prizelist) // 将列表中非谢谢参与的奖品提前
              if (prizelist) prizeObj = prizelist[0]
              const date = dateFormat(timestamp, 'yyyy-MM-dd')
              this.updateSignInList([...this.signInList, { signInDate: date, signInPrize: prizeObj }])
              this.updateIsSign(true)
              const time = this.testTime ? this.currentDate : timestamp
              if (prizeObj && prizeObj.prizeId && String(prizeObj.prizeType) !== '5') {
                this.prizePopup(time, prizelist, prizeObj.subtitle)
              } else {
                this.noPrizePopup(time)
              }
              this.queryTjlbInfo() // 查询满签状态，但是会跟天降礼包的领取撞上，需要对天降礼包的弹窗状态做处理
            }
          })
          .catch((err) => {
            this.getResult(err.result)
          })
          .finally(() => {
            this.$loading.hide()
          })
      }
    },
    // 展示签到
    showCheckinPrize(prizelist, timestamp) {
      prizelist = listGetHavePrize(prizelist) // 将列表中非谢谢参与的奖品提前
      const prize = prizelist[0]
      const date = dateFormat(timestamp, 'yyyy-MM-dd')
      this.updateSignInList([...this.signInList, { signInDate: date, signInPrize: prize }])
      this.updateIsSign(true)
      const time = this.testTime ? this.currentDate : timestamp
      if (prize.prizeId && String(prize.prizeType) !== '5') {
        this.prizePopup(time, prizelist, prize.subtitle)
      } else {
        this.noPrizePopup(time)
      }
    },
    // 成功翻牌子
    doPrizeFpz(timestamp) {
      this.usedCount += 1
      this.count -= 1
      if (timestamp && !this.testTime) {
        this.updateCurrentdate(timestamp)
      }
    },
    // 成功完成浏览任务
    doTaskSuccess() {
      this.count += 1
      this.visitStatus = true
      this.visitShare = true
    },
    // 删除分享提示
    deleteNotice(opr) {
      deleteNotice({ token: this.userToken, opr }).then((res) => {})
    },
    // 展示升级弹窗
    showUpgrade() {
      this.$refs.upgrade.showPopup()
    },
    getResult(result) {
      if(String(result) === '-114') {
        this.updateIsSign(true)
      }
      return getcheckinResult(result)
    },
    // 底部banner跳转
    gotoBannerUrl(item) {
      jtWebtrends1.multiTrack('P00000025022', `中国移动APP每日签到_底部banner_${item.title}`, { nextUrl: item.url })
      if (!checkBjUser()) return
      newWebview(item.url)
    },
    changeShowGradePopup() {
      this.showGradePopup = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/projects/checkin-new/styles/home.scss';
@import '~@/projects/checkin-new/styles/mixin.scss';
.index {
  background: linear-gradient(90deg, #ff3445, #ff6651);
}
::v-deep .van-popup {
  overflow-y: visible;
}
.waistbands {
  width: 640px;
  margin: 20px auto auto;
}
.family-task {
  width: 100%;
  width: 640px;
  height: 150px;
  margin: 20px auto auto;
  background-position: center center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.is-iframe {
  width: 1px;
  height: 1px;
  display: none;
}
</style>
