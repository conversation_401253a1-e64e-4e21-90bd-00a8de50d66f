<template>
  <div class="collection">
    <div v-if="showGif" class="gif-box">
      <img :src="getUrl('close.png')" alt="" class="close" @click="showGif = false" />
      <img :src="getUrl('xf.gif')" alt="" class="xf-gif" @click="gifClick">
    </div>
    <div v-if="!gradeValue && ((showGradePopup && count === 1 && userGradePopUpState) || showPopup)" class="grade-popup">
      <div class="warp">
        <img :src="getUrl('1.png')" class="icon1" alt="">
        <img :src="getUrl('2.png')" class="icon2" alt="">
        <img :src="getUrl('3.png')" class="icon3" alt="">
        <img :src="getUrl('4.png')" class="icon4" alt="">
        <img :src="getUrl('6.png')" class="icon6" alt="">
        <div class="main">
          <img :src="getUrl('title.png')" class="main-title" alt="">
          <p class="main-text">您对中国移动手机营业厅的服务<br>是否满意，可以打几分？</p>
          <div class="grade-box">
            <div class="grade-box-one-row">
              <p>非常不满意</p>
              <img :src="getUrl('arrows.png')" class="arrows" />
              <p>非常满意</p>
            </div>
            <div class="grade-box-two-row">
              <div
                v-for="item in 10"
                @click="selectGrade(item)"
                :class="['item', item <= userSelectGrade ? 'active' : '', userSelectGrade ? 'select':'']"
              >
                {{ item }}
              </div>
            </div>
            <img
              v-if="!userSelectGrade"
              :src="getUrl('gift.png')"
              class="no-open"
            />
            <img
              v-else
              :src="getUrl('yet-open.png')"
              :class="`yet-open yet-open${userSelectGrade}`"
            />
            <img v-if="!userSelectGrade" class="hand" :src="getUrl('hand.png')" alt="" @click="selectGrade(10)">
          </div>
          <p class="main-tips">PS:10分为满意，9分-1分不满意程度渐强</p>
          <div class="btn-box">
            <div class="btn close" @click="closePopup">
              <p>取消</p>
              <img :src="getUrl('btn-close.png')" alt="">
            </div>
            <div class="btn submit" @click="submit">
              <p>提交</p>
              <img :src="getUrl('btn-bg.png')" alt="">
            </div>
          </div>
          <div class="main-tips1">评分可获得1份心意礼，可前往“我的-卡券"领取</div>
          <div v-if="showGradePopup" class="main-remind" @click="selectRemindClick">
            <img v-if="selectRemind" :src="getUrl('selsect.png')" alt="">
            <div v-else-if="!selectRemind"></div>
            <p>本月不再提醒</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CHANNEL from '../../../../../../../../../bjapp-model/vue2/js/channel'
import { newWebview } from '../../../../../../../../../bjapp-model/vue2/js/jt-app-ability'
import { queryUserGradePopUp, setUserGradePopUpState, grade } from './api/collection-suspend'
import jtWebtrends1 from '@/utils/jtWebtrends'
export default {
  props: {
    showGradePopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userToken: sessionStorage.getItem('userToken'),
      showGif: true, // 是否展示悬浮动图
      showPopup: false, // 点击悬浮动图打开的弹窗
      gradeValue: 0, // 用户已经评的分
      count: 0, // 每天只能展示一次弹窗
      userSelectGrade: 0, // 用户选择的分数
      selectRemind: false, // 是否选择本月不提醒（只针对奖品弹窗）
      userGradePopUpState: 0 // 奖品弹窗后续是否展示本弹窗
    }
  },
  computed: {},
  watch: {
    showGradePopup(newV) {
      if(newV) {
        setUserGradePopUpState({token: this.userToken, type: 'day'}).then(() => {})
        this.count++
      }
    }
  },
  mounted(){
    queryUserGradePopUp({token: this.userToken}).then(res => {
      this.userGradePopUpState = Number(res.userGradePopUpState)
      this.gradeValue = res.userGradeValue
      console.log('打印接口返回: ', this.userGradePopUpState, this.gradeValue)
    })
  },
  methods: {
    getUrl(name = '') {
      let url = 'https://h5.bj.10086.cn/cmcc_activity/collection-card/index.html?channel=JT'
      if(CHANNEL.isGray() && name) {
        url = `https://st.bj.chinamobile.com:7443/shortAct/collection-card/static/img/${name}`
      } else if(CHANNEL.isGray() && !name) {
        url = 'https://st.bj.chinamobile.com:7443/shortAct/collection-card/index.html?channel=JT'
      } else if(!CHANNEL.isGray() && name) {
        url = `https://h5.bj.10086.cn/cmcc_activity/collection-card/static/img/${name}`
      }
      return url
    },
    // 选择对应分数
    selectGrade(item) {
      this.userSelectGrade = item
    },
    // 选择本月不提醒
    selectRemindClick() {
      this.selectRemind = !this.selectRemind
    },
    // 点击悬浮动图
    gifClick() {
      jtWebtrends1.multiTrack('P00000094126', '中国移动APP_签到_悬浮窗_满意度专区')
      if (this.gradeValue) {
        let url = this.getUrl()
        newWebview(url)
      } else {
        this.showPopup = true
      }
    },
    // 关闭弹窗
    closePopup() {
      if(this.showGradePopup) {
        this.$emit('update:showGradePopup', false)
      }
      if(this.showPopup) {
        this.showPopup = false
        this.userSelectGrade = 0
      }
    },
    // 提交评分
    submit() {
      if(!this.userSelectGrade) {
        this.$toast('请给手机营业厅进行打分')
      } else {
        console.log('提交评分')
        if(this.selectRemind) {
          setUserGradePopUpState({token: this.userToken, type: 'month'}).then(() => {})
        }
        grade({
          token: this.userToken,
          gradeValue: this.userSelectGrade,
          source: 'checkin'
        }).then(res => {
          if(res.result === 0) {
            this.$toast('感谢您的评价，请前往“我的-卡券”查看奖励~')
            this.gradeValue = this.userSelectGrade
            this.userGradePopUpState = 0
            this.closePopup()
          } else {
            this.$toast(res.errmsg || '系统繁忙，请稍后再试~')
            this.closePopup()
          }
        })
        .catch((err) => {
          this.$toast(err.errmsg || '系统繁忙，请稍后再试~')
          this.closePopup()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.collection {
  z-index: 3;
  position: relative;
  .gif-box {
    position: fixed;
    bottom: 10px;
    right: 40px;
    z-index: 1;
    .close {
      position: absolute;
      width: 40px;
      height: 40px;
      top: -40px;
      right: 0;
    }
    .xf-gif {
      width: 200px;
      height: 200px;
    }
  }
  .grade-popup {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1111;
    .warp {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 636px;
      background-image: linear-gradient(-34deg,
          #d3d5fa 0%,
          #b0d5fa 20%,
          #8dd4f9 44%,
          #d2d5fb 100%),
        linear-gradient(
          #ffffff,
          #ffffff);
      border-radius: 10px;
      border: solid 2px #ffffff;
      padding: 22px 15px;
      .icon1 {
        position: absolute;
        left: -1%;
        top: 10%;
        width: 45px;
        height: 27px;
        z-index: 1;
      }
      .icon2 {
        position: absolute;
        right: -4%;
        top: 12%;
        width: 112px;
        height: 108px;
        z-index: 1;
      }
      .icon3 {
        position: absolute;
        left: -1%;
        top: 60%;
        width: 32px;
        height: 35px;
        z-index: 1;
      }
      .icon4 {
        position: absolute;
        right: -1%;
        top: 42%;
        width: 31px;
        height: 31px;
        z-index: 1;
      }
      .icon6 {
        position: absolute;
        width: 90px;
        height: 90px;
        right: -3%;
        top: 70%;
        rotate: 90deg;
        z-index: 1;
      }
      .main {
        position: relative;
        width: 100%;
        background: #fafcff;
        border-radius: 10px;
        padding-top: 200px;
        &-title {
          position: absolute;
          left: 0;
          top: 80px;
          width: 530px;
          height: 130px;
        }
        &-text {
          font-size: 32px;
          line-height: 43px;
          color: #272726;
        }
        .grade-box {
          position: relative;
          width: 500px;
          height: 102px;
          margin: 40px auto 20px;
          border: 1px solid #999;
          border-radius: 10px;
          &-one-row {
            font-size: 16px;
            letter-spacing: 0px;
            color: #2e3334;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            height: 50px;
            border-bottom: 1px solid #999;
            padding-left: 20px;
            .arrows {
              width: 200px;
              height: 8px;
              margin: 0 30px;
            }
          }
          &-two-row {
            position: relative;
            display: flex;
            .item {
              position: relative;
              font-size: 16px;
              width: 50px;
              height: 48px;
              line-height: 50px;
              border-right: 1px solid #999;
              background: #fafcff;
            }
            .item:first-child {
              border-bottom-left-radius: 10px;
            }
            .item:last-child {
              border-right: none;
              border-bottom-right-radius: 10px;
              background: #fbc0e3;
            }
            .select:last-child {
              background: #fafcff;
            }
            .active {
              background: #fcfebd;
            }
            .active:last-child {
              background: #fcfebd;
            }
          }
          .no-open {
            position: absolute;
            top: -27px;
            right: -24px;
            width: 165px;
            height: 68px;
          }
          .yet-open {
            position: absolute;
            top: -44px;
            left: -22px;
            width: 90px;
            height: 100px;
          }
          .yet-open2 {
            left: 32px;
          }
          .yet-open3 {
            left: 82px;
          }
          .yet-open4 {
            left: 132px;
          }
          .yet-open5 {
            left: 182px;
          }
          .yet-open6 {
            left: 232px;
          }
          .yet-open7 {
            left: 282px;
          }
          .yet-open8 {
            left: 332px;
          }
          .yet-open9 {
            left: 382px;
          }
          .yet-open10 {
            left: 432px;
          }
          .hand {
            position: absolute;
            top: 70px;
            right: -40px;
            width: 66px;
            height: 50px;
            animation: scaleFn 0.5s linear alternate infinite;
          }
        }
        &-tips {
          font-size: 22px;
          color: #5a5a5a;
        }
        .btn-box {
          letter-spacing: 1px;
          font-size: 30px;
          display: flex;
          justify-content: space-evenly;
          margin: 30px 0 10px;
          .btn {
            position: relative;
            width: 255px;
            height: 80px;
            p {
              position: relative;
              z-index: 1;
              line-height: 67px;
            }
            img {
              top: 0;
              left: 0;
              position: absolute;
              width: 255px;
              height: 80px;
            }
          }
          .close {
            p {
              line-height: 80px;
              color: #219def;
            }
          }
          .submit {
            p {
              color: #fff;
            }
          }
        }
        &-tips1 {
          font-size: 20px;
          color: #117edd;
          padding-bottom: 40px;
        }
        &-remind {
          padding-bottom: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          color: #575654;
          img {
            width: 24px;
            height: 24px;
          }
          div {
            width: 24px;
            height: 24px;
            border: 1px solid #117edd;
          }
          p {
            margin-left: 10px;
          }
        }
      }
    }
  }
}
@keyframes scaleFn {
  0% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}
</style>
