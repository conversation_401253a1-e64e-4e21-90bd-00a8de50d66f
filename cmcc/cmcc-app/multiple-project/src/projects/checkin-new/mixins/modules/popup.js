/**
 * Created by linsang on 2020/10/27.
 */
// import CHANNEL from '@/utils/channel'
import { actRule, thinActRule, actRule1, feedbackRule } from '../../config/rule'
import jtWebtrends1 from '@/utils/jtWebtrends'

export default {
  methods: {
    // 使用规则
    useRulePopup (ruleText) {
      this.$ruleDialog.show({
        title: '使用规则',
        content: ruleText,
        closeBtn: true,
        btn: '知道了',
        callBack: () => {
          this.$aspDialog.hide()
          jtWebtrends1.multiTrack('P00000025027', '中国移动APP每日签到_中奖记录页_使用规则_知道了')
        }
      })
    },
    // 活动规则
    rulePopup (content) {
      this.$ruleDialog.show({
        title: '活动规则',
        content,
        closeBtn: true,
        btn: '返回签到页面',
        dialogStyle: {
          color: '#010101'
        },
        callBack: () => {
          jtWebtrends1.multiTrack('P00000025011', '中国移动APP每日签到_活动规则_返回')
          this.$aspDialog.hide()
        }
      })
    },
    feedbackRulePopup () {
      this.$ruleDialog.show({
        title: '温馨提示',
        content: feedbackRule,
        closeBtn: true,
        btn: '返回',
        dialogStyle: {
          color: '#010101'
        },
        callBack: () => {
          this.$aspDialog.hide()
        }
      })
    },
    // 手厅简化版签到活动规则
    thinRulePopup () {
      this.$ruleDialog.show({
        title: '活动规则',
        content: thinActRule,
        closeBtn: true,
        btn: '返回',
        dialogStyle: {
          color: '#010101'
        },
        callBack: () => {
          jtWebtrends1.multiTrack('P00000025011', '中国移动APP每日签到_活动规则_返回')
          this.$aspDialog.hide()
        }
      })
    },
    // 签到奖品弹窗
    prizePopup (currentTime, prizelist, desc) {
      const tPrizelist = prizelist.filter(item => String(item.prizeType) !== '5') // 只展示非谢谢参与的奖品
      // const tDesc = tPrizelist.length > 1 ? { topDesc: '恭喜您获得以下签到奖励', noShowDesc: true } : { desc: desc }
      const tDesc = { topDesc: '恭喜您获得以下签到奖励', noShowDesc: true }
      this.$aspDialog.show({
        type: 'prizePopup',
        title: '签到成功',
        content: '',
        dialogStyle: {
          alignItems: 'center',
          textAlign: 'center',
          justifyContent: 'center',
          display: 'flex'
        },
        btn: '我知道了',
        ...tDesc,
        currentTime,
        prizeObj: tPrizelist[0],
        prizeObjTwo: tPrizelist[1],
        closeBtn: true,
        isSimple: false,
        callBack: () => {
          // this.$router.push({ name: 'Prize' })
          this.showGradePopup = true
          // Webtrends1.multiTrack('220613_QDGB_JHY_QDHJTC_CKJP')
        },
        closeCallBack: () => {
          this.showGradePopup = true
        }
      })
    },
    // 签到奖品弹窗
    otherPrizePopup (desc, prizeObj, chama = {}) {
      this.$aspDialog.show({
        title: '恭喜您',
        content: '',
        dialogStyle: {
          alignItems: 'center',
          textAlign: 'center',
          justifyContent: 'center',
          display: 'flex'
        },
        desc: desc,
        btn: '查看奖品',
        currentTime: new Date().getTime(),
        prizeObj: prizeObj,
        closeBtn: true,
        isSimple: false,
        callBack: () => {
          this.$router.push({ name: 'Prize' })
          if (chama && chama.event && chama.envName) {
            jtWebtrends1.multiTrack(chama.event, chama.envName)
          }
          // Webtrends1.multiTrack('220613_QDGB_JHY_QDHJTC_CKJP')
        }
      })
    },
    // 签到奖品弹窗
    thankPopup (prizeObj) {
      this.$aspDialog.show({
        title: '很遗憾',
        content: '',
        dialogStyle: {
          alignItems: 'center',
          textAlign: 'center',
          justifyContent: 'center',
          display: 'flex'
        },
        desc: '别灰心，下月再来吧！',
        btn: '关闭',
        currentTime: new Date().getTime(),
        prizeObj: prizeObj,
        closeBtn: true,
        isSimple: false,
        callBack: () => {
          // this.$router.push({ name: 'Prize' })
          // Webtrends1.multiTrack('220613_QDGB_JHY_QDHJTC_CKJP')
        }
      })
    },
    // 签到未获奖
    noPrizePopup (currentTime) {
      this.$aspDialog.show({
        title: '签到成功',
        content: '今日签到任务已完成！',
        dialogStyle: {
          alignItems: 'center',
          textAlign: 'center',
          justifyContent: 'center',
          display: 'flex',
          height: '40px'
        },
        btn: '',
        closeBtn: true,
        isSimple: false,
        showBtn: false,
        currentTime,
        callBack: () => {
          this.showGradePopup = true
          // Webtrends1.multiTrack('210910_STCHWLHD_JHY_TC_BKCYTC_ZDL')
        },
        closeCallBack: () => {
          this.showGradePopup = true
        }
      })
    }
  }
}
