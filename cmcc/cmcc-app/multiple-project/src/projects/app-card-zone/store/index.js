import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 用户选择的号码
    checkNumber: '',
    // 用户选择号码的类型(主要用于tab栏)
    checkType: '',
    // 用户选择的号码对应排序数字(主要用于tab栏)
    activeIndex: '',
    // 用户选择的类型（例如：芒果卡、稀有靓号等）
    desc: '',
    mangoList: [], // 将芒果卡的所有号码数据存到一个列表
    aiBottom: 0
  },
  mutations: {
    UPDATE_AIBOTTOM(state, val) {
      state.aiBottom = val
    },
    UPDATE_CHECKNUMBER(state, val) {
      state.checkNumber = val
    },
    UPDATE_CHECKTYPE(state, val) {
      state.checkType = val
    },
    UPDATE_ACTIVECHECKINDEX(state, val) {
      state.activeIndex = val
    },
    UPDATE_USERCHECKDATA(state, val) {
      state.checkNumber = val.checkNumber
      state.checkType = val.checkType
      state.desc = val.desc
      state.activeIndex = val.activeIndex
    },
    CLEAN_USERCHECKDATA(state) {
      state.checkNumber = ''
      state.checkType = ''
      state.desc = ''
      state.activeIndex = ''
    },
    UPDATE_MANGOLIST(state, val) {
      state.mangoList = val
    }
  },
  actions: {
    // 更新号码
    updateCheckNumber({ commit }, val) {
      commit('UPDATE_CHECKNUMBER', val)
    },
    // 更新类型
    updateCheckTpye({ commit }, val) {
      commit('UPDATE_CHECKTYPE', val)
    },
    // 更新序号
    updateActiveCheckIndex({ commit }, val) {
      commit('UPDATE_ACTIVECHECKINDEX', val)
    },
    // 更新全部数据
    updateUserCheckData({ commit }, val) {
      commit('UPDATE_USERCHECKDATA', val)
    },
    // 重置全部数据
    cleanUserCheckData({ commit }) {
      commit('CLEAN_USERCHECKDATA')
    },
    // 更新芒果卡列表
    updateMangoList({ commit }, val) {
      commit('UPDATE_MANGOLIST', val)
    }
  }
})
